"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ArrowLeft, Shield, Plus, Settings, LogOut } from "lucide-react";
import { AuthModal } from "@/components/AuthModal";
import { logout, getCurrentUser } from "@/lib/auth";

export default function SettingsPage() {
  const router = useRouter();
  const [adminMode, setAdminMode] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [currentUser, setCurrentUser] = useState<string | null>(null);

  // Load admin mode from localStorage on component mount
  useEffect(() => {
    const savedAdminMode = localStorage.getItem("adminMode") === "true";
    setAdminMode(savedAdminMode);
    setCurrentUser(getCurrentUser());
  }, []);

  const handleAdminModeChange = (checked: boolean) => {
    if (checked) {
      // Show authentication modal when turning on admin mode
      setShowAuthModal(true);
    } else {
      // Log out when turning off admin mode
      logout();
      setAdminMode(false);
      setCurrentUser(null);
    }
  };

  const handleAuthSuccess = () => {
    setAdminMode(true);
    setCurrentUser(getCurrentUser());
    setShowAuthModal(false);
  };

  const handleLogout = () => {
    logout();
    setAdminMode(false);
    setCurrentUser(null);
  };

  const handleAddTemplate = () => {
    if (adminMode) {
      router.push("/templates/add");
    }
  };

  const handleManageTemplates = () => {
    if (adminMode) {
      router.push("/templates/manage");
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <div className="flex items-center gap-4 mb-8">
        <Button variant="outline" size="icon" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-semibold text-foreground">Settings</h1>
          <p className="text-muted-foreground">
            Manage your application preferences and template settings.
          </p>
        </div>
      </div>

      <div className="space-y-6">
        {/* Admin Mode */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Admin Mode
            </CardTitle>
            <CardDescription>
              Enable admin mode to access advanced features and settings.
              {currentUser && ` Currently logged in as: ${currentUser}`}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="admin-mode"
                checked={adminMode}
                onCheckedChange={handleAdminModeChange}
              />
              <Label htmlFor="admin-mode">
                {adminMode ? "Admin mode enabled" : "Admin mode disabled"}
              </Label>
            </div>

            {adminMode && currentUser && (
              <Button
                onClick={handleLogout}
                variant="outline"
                size="sm"
                className="w-full"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Logout
              </Button>
            )}
          </CardContent>
        </Card>

        {/* Template Management */}
        <Card>
          <CardHeader>
            <CardTitle>Template Management</CardTitle>
            <CardDescription>
              Add new templates or manage existing ones.{" "}
              {!adminMode && "Admin mode required."}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              onClick={handleAddTemplate}
              className="w-full justify-start"
              variant="outline"
              disabled={!adminMode}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Template
            </Button>

            <Button
              onClick={handleManageTemplates}
              className="w-full justify-start"
              variant="outline"
              disabled={!adminMode}
            >
              <Settings className="h-4 w-4 mr-2" />
              Manage Templates
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Authentication Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        onSuccess={handleAuthSuccess}
      />
    </div>
  );
}
