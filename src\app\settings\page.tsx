"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ArrowLeft, Shield, Plus, Settings } from "lucide-react";

export default function SettingsPage() {
  const router = useRouter();
  const [adminMode, setAdminMode] = useState(false);

  const handleAddTemplate = () => {
    router.push("/templates/add");
  };

  const handleManageTemplates = () => {
    router.push("/templates/manage");
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <div className="flex items-center gap-4 mb-8">
        <Button
          variant="outline"
          size="icon"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-semibold text-foreground">Settings</h1>
          <p className="text-muted-foreground">
            Manage your application preferences and template settings.
          </p>
        </div>
      </div>

      <div className="space-y-6">
        {/* Admin Mode */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Admin Mode
            </CardTitle>
            <CardDescription>
              Enable admin mode to access advanced features and settings.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Switch
                id="admin-mode"
                checked={adminMode}
                onCheckedChange={setAdminMode}
              />
              <Label htmlFor="admin-mode">
                {adminMode ? "Admin mode enabled" : "Admin mode disabled"}
              </Label>
            </div>
          </CardContent>
        </Card>

        {/* Template Management */}
        <Card>
          <CardHeader>
            <CardTitle>Template Management</CardTitle>
            <CardDescription>
              Add new templates or manage existing ones.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              onClick={handleAddTemplate}
              className="w-full justify-start"
              variant="outline"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Template
            </Button>
            
            <Button
              onClick={handleManageTemplates}
              className="w-full justify-start"
              variant="outline"
            >
              <Settings className="h-4 w-4 mr-2" />
              Manage Templates
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
