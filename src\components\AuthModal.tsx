"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Eye, EyeOff, Copy, Key, Shield, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { 
  login, 
  signup, 
  recoverPassword, 
  getUserRecoveryOptions,
  generatePrivateKey,
  SECURITY_QUESTIONS,
  type SignupData,
  type LoginCredentials,
  type RecoveryData
} from "@/lib/auth";

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

type AuthMode = 'login' | 'signup' | 'forgot';
type RecoveryMethod = 'privateKey' | 'securityQuestions';

export function AuthModal({ isOpen, onClose, onSuccess }: AuthModalProps) {
  const [mode, setMode] = useState<AuthMode>('login');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  // Login state
  const [loginData, setLoginData] = useState<LoginCredentials>({
    username: '',
    password: ''
  });

  // Signup state
  const [signupData, setSignupData] = useState<SignupData>({
    username: '',
    password: '',
    recoveryMethod: 'privateKey',
    securityQuestions: []
  });
  const [confirmPassword, setConfirmPassword] = useState('');
  const [generatedPrivateKey, setGeneratedPrivateKey] = useState('');
  const [showPrivateKey, setShowPrivateKey] = useState(false);

  // Forgot password state
  const [forgotUsername, setForgotUsername] = useState('');
  const [recoveryData, setRecoveryData] = useState<RecoveryData>({
    username: '',
    recoveryMethod: 'privateKey'
  });
  const [newPassword, setNewPassword] = useState('');
  const [confirmNewPassword, setConfirmNewPassword] = useState('');
  const [recoveryStep, setRecoveryStep] = useState<'username' | 'method' | 'verify' | 'reset'>('username');
  const [userRecoveryOptions, setUserRecoveryOptions] = useState<{ hasPrivateKey: boolean; securityQuestions: string[] }>({
    hasPrivateKey: false,
    securityQuestions: []
  });

  const resetForm = () => {
    setLoginData({ username: '', password: '' });
    setSignupData({ username: '', password: '', recoveryMethod: 'privateKey', securityQuestions: [] });
    setConfirmPassword('');
    setGeneratedPrivateKey('');
    setShowPrivateKey(false);
    setForgotUsername('');
    setRecoveryData({ username: '', recoveryMethod: 'privateKey' });
    setNewPassword('');
    setConfirmNewPassword('');
    setRecoveryStep('username');
    setUserRecoveryOptions({ hasPrivateKey: false, securityQuestions: [] });
    setShowPassword(false);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const handleLogin = async () => {
    if (!loginData.username || !loginData.password) {
      toast.error('Please fill in all fields');
      return;
    }

    setIsLoading(true);
    const result = login(loginData);
    setIsLoading(false);

    if (result.success) {
      toast.success(result.message);
      onSuccess();
      handleClose();
    } else {
      toast.error(result.message);
    }
  };

  const handleSignup = async () => {
    if (!signupData.username || !signupData.password || !confirmPassword) {
      toast.error('Please fill in all fields');
      return;
    }

    if (signupData.password !== confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    if (signupData.password.length < 6) {
      toast.error('Password must be at least 6 characters long');
      return;
    }

    if (signupData.recoveryMethod === 'securityQuestions') {
      if (!signupData.securityQuestions || signupData.securityQuestions.length !== 3) {
        toast.error('Please answer all 3 security questions');
        return;
      }
      
      for (const sq of signupData.securityQuestions) {
        if (!sq.answer.trim()) {
          toast.error('Please answer all security questions');
          return;
        }
      }
    }

    setIsLoading(true);
    const result = signup(signupData);
    setIsLoading(false);

    if (result.success) {
      toast.success(result.message);
      
      if (result.privateKey) {
        setGeneratedPrivateKey(result.privateKey);
        setShowPrivateKey(true);
        return; // Don't close modal, show private key
      }
      
      onSuccess();
      handleClose();
    } else {
      toast.error(result.message);
    }
  };

  const handleForgotPassword = async () => {
    if (recoveryStep === 'username') {
      if (!forgotUsername) {
        toast.error('Please enter your username');
        return;
      }

      const options = getUserRecoveryOptions(forgotUsername);
      if (!options.hasPrivateKey && options.securityQuestions.length === 0) {
        toast.error('No recovery options found for this user');
        return;
      }

      setUserRecoveryOptions(options);
      setRecoveryData({ ...recoveryData, username: forgotUsername });
      setRecoveryStep('method');
    } else if (recoveryStep === 'method') {
      setRecoveryStep('verify');
    } else if (recoveryStep === 'verify') {
      if (recoveryData.recoveryMethod === 'privateKey' && !recoveryData.privateKey) {
        toast.error('Please enter your private key');
        return;
      }
      
      if (recoveryData.recoveryMethod === 'securityQuestions' && 
          (!recoveryData.securityAnswers || recoveryData.securityAnswers.some(answer => !answer.trim()))) {
        toast.error('Please answer all security questions');
        return;
      }

      setRecoveryStep('reset');
    } else if (recoveryStep === 'reset') {
      if (!newPassword || !confirmNewPassword) {
        toast.error('Please fill in all fields');
        return;
      }

      if (newPassword !== confirmNewPassword) {
        toast.error('Passwords do not match');
        return;
      }

      if (newPassword.length < 6) {
        toast.error('Password must be at least 6 characters long');
        return;
      }

      setIsLoading(true);
      const result = recoverPassword(recoveryData, newPassword);
      setIsLoading(false);

      if (result.success) {
        toast.success(result.message);
        setMode('login');
        resetForm();
      } else {
        toast.error(result.message);
      }
    }
  };

  const generateNewPrivateKey = () => {
    const newKey = generatePrivateKey();
    setSignupData({ ...signupData, privateKey: newKey });
  };

  const copyPrivateKey = () => {
    navigator.clipboard.writeText(generatedPrivateKey);
    toast.success('Private key copied to clipboard');
  };

  const addSecurityQuestion = (index: number, question: string, answer: string) => {
    const newQuestions = [...(signupData.securityQuestions || [])];
    newQuestions[index] = { question, answer };
    setSignupData({ ...signupData, securityQuestions: newQuestions });
  };

  if (showPrivateKey) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Key className="h-5 w-5" />
              Save Your Private Key
            </DialogTitle>
            <DialogDescription>
              This is your recovery private key. Save it securely - you'll need it to recover your account if you forget your password.
            </DialogDescription>
          </DialogHeader>
          
          <Card className="border-amber-200 bg-amber-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-2 mb-4">
                <AlertCircle className="h-5 w-5 text-amber-600" />
                <span className="text-sm font-medium text-amber-800">Important: Save this key safely!</span>
              </div>
              
              <div className="bg-white p-3 rounded border font-mono text-sm break-all">
                {generatedPrivateKey}
              </div>
              
              <Button
                onClick={copyPrivateKey}
                variant="outline"
                size="sm"
                className="mt-3 w-full"
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy Private Key
              </Button>
            </CardContent>
          </Card>

          <div className="flex gap-2">
            <Button onClick={() => { setShowPrivateKey(false); onSuccess(); handleClose(); }} className="flex-1">
              I've Saved My Key
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            {mode === 'login' && 'Admin Login'}
            {mode === 'signup' && 'Create Admin Account'}
            {mode === 'forgot' && 'Forgot Password'}
          </DialogTitle>
          <DialogDescription>
            {mode === 'login' && 'Enter your credentials to access admin features'}
            {mode === 'signup' && 'Create a new admin account with recovery options'}
            {mode === 'forgot' && 'Recover your account using your recovery method'}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {mode === 'login' && (
            <>
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  value={loginData.username}
                  onChange={(e) => setLoginData({ ...loginData, username: e.target.value })}
                  placeholder="Enter your username"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={loginData.password}
                    onChange={(e) => setLoginData({ ...loginData, password: e.target.value })}
                    placeholder="Enter your password"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              <div className="flex gap-2">
                <Button onClick={handleLogin} disabled={isLoading} className="flex-1">
                  {isLoading ? 'Logging in...' : 'Login'}
                </Button>
              </div>

              <div className="text-center space-y-2">
                <Button variant="link" onClick={() => setMode('forgot')} className="text-sm">
                  Forgot Password?
                </Button>
                <div className="text-sm text-muted-foreground">
                  Don't have an account?{' '}
                  <Button variant="link" onClick={() => setMode('signup')} className="p-0 h-auto text-sm">
                    Sign up
                  </Button>
                </div>
              </div>
            </>
          )}

          {mode === 'signup' && (
            <>
              <div className="space-y-2">
                <Label htmlFor="signup-username">Username</Label>
                <Input
                  id="signup-username"
                  value={signupData.username}
                  onChange={(e) => setSignupData({ ...signupData, username: e.target.value })}
                  placeholder="Choose a username"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="signup-password">Password</Label>
                <div className="relative">
                  <Input
                    id="signup-password"
                    type={showPassword ? "text" : "password"}
                    value={signupData.password}
                    onChange={(e) => setSignupData({ ...signupData, password: e.target.value })}
                    placeholder="Create a password (min 6 characters)"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirm-password">Confirm Password</Label>
                <Input
                  id="confirm-password"
                  type={showPassword ? "text" : "password"}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="Confirm your password"
                />
              </div>

              <div className="space-y-2">
                <Label>Recovery Method</Label>
                <Select
                  value={signupData.recoveryMethod}
                  onValueChange={(value: RecoveryMethod) => 
                    setSignupData({ ...signupData, recoveryMethod: value, securityQuestions: [] })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="privateKey">Private Key</SelectItem>
                    <SelectItem value="securityQuestions">Security Questions</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {signupData.recoveryMethod === 'privateKey' && (
                <Card className="border-blue-200 bg-blue-50">
                  <CardContent className="pt-4">
                    <p className="text-sm text-blue-800 mb-3">
                      A private key will be generated for account recovery. Save it securely!
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={generateNewPrivateKey}
                      className="w-full"
                    >
                      <Key className="h-4 w-4 mr-2" />
                      Generate Custom Key (Optional)
                    </Button>
                  </CardContent>
                </Card>
              )}

              {signupData.recoveryMethod === 'securityQuestions' && (
                <div className="space-y-3">
                  <Label className="text-sm font-medium">Security Questions (Choose 3)</Label>
                  {[0, 1, 2].map((index) => (
                    <div key={index} className="space-y-2">
                      <Select
                        value={signupData.securityQuestions?.[index]?.question || ''}
                        onValueChange={(question) => {
                          const currentAnswer = signupData.securityQuestions?.[index]?.answer || '';
                          addSecurityQuestion(index, question, currentAnswer);
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={`Select question ${index + 1}`} />
                        </SelectTrigger>
                        <SelectContent>
                          {SECURITY_QUESTIONS.map((question) => (
                            <SelectItem key={question} value={question}>
                              {question}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {signupData.securityQuestions?.[index]?.question && (
                        <Input
                          placeholder="Your answer"
                          value={signupData.securityQuestions[index]?.answer || ''}
                          onChange={(e) => {
                            const currentQuestion = signupData.securityQuestions?.[index]?.question || '';
                            addSecurityQuestion(index, currentQuestion, e.target.value);
                          }}
                        />
                      )}
                    </div>
                  ))}
                </div>
              )}

              <div className="flex gap-2">
                <Button onClick={handleSignup} disabled={isLoading} className="flex-1">
                  {isLoading ? 'Creating Account...' : 'Create Account'}
                </Button>
              </div>

              <div className="text-center">
                <div className="text-sm text-muted-foreground">
                  Already have an account?{' '}
                  <Button variant="link" onClick={() => setMode('login')} className="p-0 h-auto text-sm">
                    Login
                  </Button>
                </div>
              </div>
            </>
          )}

          {mode === 'forgot' && (
            <>
              {recoveryStep === 'username' && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="forgot-username">Username</Label>
                    <Input
                      id="forgot-username"
                      value={forgotUsername}
                      onChange={(e) => setForgotUsername(e.target.value)}
                      placeholder="Enter your username"
                    />
                  </div>
                  <Button onClick={handleForgotPassword} disabled={isLoading} className="w-full">
                    Continue
                  </Button>
                </>
              )}

              {recoveryStep === 'method' && (
                <>
                  <div className="space-y-3">
                    <Label>Choose Recovery Method</Label>
                    {userRecoveryOptions.hasPrivateKey && (
                      <Button
                        variant={recoveryData.recoveryMethod === 'privateKey' ? 'default' : 'outline'}
                        onClick={() => setRecoveryData({ ...recoveryData, recoveryMethod: 'privateKey' })}
                        className="w-full justify-start"
                      >
                        <Key className="h-4 w-4 mr-2" />
                        Use Private Key
                      </Button>
                    )}
                    {userRecoveryOptions.securityQuestions.length > 0 && (
                      <Button
                        variant={recoveryData.recoveryMethod === 'securityQuestions' ? 'default' : 'outline'}
                        onClick={() => setRecoveryData({ ...recoveryData, recoveryMethod: 'securityQuestions' })}
                        className="w-full justify-start"
                      >
                        <Shield className="h-4 w-4 mr-2" />
                        Answer Security Questions
                      </Button>
                    )}
                  </div>
                  <Button onClick={handleForgotPassword} className="w-full">
                    Continue
                  </Button>
                </>
              )}

              {recoveryStep === 'verify' && (
                <>
                  {recoveryData.recoveryMethod === 'privateKey' && (
                    <div className="space-y-2">
                      <Label htmlFor="private-key">Private Key</Label>
                      <Input
                        id="private-key"
                        value={recoveryData.privateKey || ''}
                        onChange={(e) => setRecoveryData({ ...recoveryData, privateKey: e.target.value })}
                        placeholder="Enter your private key"
                      />
                    </div>
                  )}

                  {recoveryData.recoveryMethod === 'securityQuestions' && (
                    <div className="space-y-3">
                      <Label>Security Questions</Label>
                      {userRecoveryOptions.securityQuestions.map((question, index) => (
                        <div key={index} className="space-y-2">
                          <Label className="text-sm">{question}</Label>
                          <Input
                            placeholder="Your answer"
                            value={recoveryData.securityAnswers?.[index] || ''}
                            onChange={(e) => {
                              const newAnswers = [...(recoveryData.securityAnswers || [])];
                              newAnswers[index] = e.target.value;
                              setRecoveryData({ ...recoveryData, securityAnswers: newAnswers });
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  )}

                  <Button onClick={handleForgotPassword} disabled={isLoading} className="w-full">
                    Verify
                  </Button>
                </>
              )}

              {recoveryStep === 'reset' && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="new-password">New Password</Label>
                    <div className="relative">
                      <Input
                        id="new-password"
                        type={showPassword ? "text" : "password"}
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        placeholder="Enter new password (min 6 characters)"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confirm-new-password">Confirm New Password</Label>
                    <Input
                      id="confirm-new-password"
                      type={showPassword ? "text" : "password"}
                      value={confirmNewPassword}
                      onChange={(e) => setConfirmNewPassword(e.target.value)}
                      placeholder="Confirm new password"
                    />
                  </div>

                  <Button onClick={handleForgotPassword} disabled={isLoading} className="w-full">
                    {isLoading ? 'Resetting...' : 'Reset Password'}
                  </Button>
                </>
              )}

              <div className="text-center">
                <Button variant="link" onClick={() => setMode('login')} className="text-sm">
                  Back to Login
                </Button>
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
