"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { FileText, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

interface Template {
  id: string;
  name: string;
  description: string;
  filename: string;
  placeholders: string[];
  layoutSize: "A4" | "Letter";
  uploadedAt: string;
}

export function DocumentOptions() {
  const router = useRouter();
  const [templates, setTemplates] = useState<Template[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      const response = await fetch("/api/templates");
      if (!response.ok) {
        throw new Error("Failed to load templates");
      }
      const data = await response.json();
      setTemplates(data);
    } catch (error) {
      console.error("Error loading templates:", error);
      toast.error("Failed to load templates");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center flex-1 px-4 py-8">
      <div className="text-center mb-8 space-y-4">
        <div className="space-y-2">
          <h1 className="text-3xl font-semibold text-foreground">Apply for?</h1>
          <p className="text-muted-foreground text-lg">
            Choose the document you need to apply for
          </p>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center min-h-[200px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading templates...</p>
          </div>
        </div>
      ) : templates.length === 0 ? (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No templates available</h3>
          <p className="text-muted-foreground mb-6">
            Add your first template to get started with document generation.
          </p>
          <Button onClick={() => router.push("/templates/add")}>
            <Plus className="h-4 w-4" />
            Add Your First Template
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl w-full">
          {templates.map((template) => (
            <Link
              key={template.id}
              href={`/templates/form/${template.id}`}
              className="group relative bg-card hover:bg-card/80 border border-border rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-200 flex flex-col items-center text-center space-y-4 min-h-[180px] hover:scale-[1.02] active:scale-[0.98]"
            >
              <div className="p-4 rounded-lg bg-primary/10 group-hover:bg-primary/15 transition-colors duration-200">
                <FileText className="h-8 w-8 text-primary" />
              </div>

              <div className="space-y-2">
                <h3 className="font-semibold text-lg text-card-foreground">
                  {template.name}
                </h3>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {template.description}
                </p>
              </div>
            </Link>
          ))}
        </div>
      )}

      <p className="text-center text-sm text-muted-foreground mt-8 max-w-md">
        Click on any button to start your application process
      </p>
    </div>
  );
}
