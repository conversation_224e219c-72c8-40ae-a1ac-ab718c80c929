"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Bell, Sun, Moon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { useRouter } from "next/navigation";
import { useTheme } from "@/components/theme-provider";

export function Header() {
  const router = useRouter();
  const { theme, setTheme } = useTheme();
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Update isDarkMode when theme changes
  useEffect(() => {
    setIsDarkMode(theme === "dark");
  }, [theme]);

  const handleThemeToggle = (checked: boolean) => {
    setIsDarkMode(checked);
    setTheme(checked ? "dark" : "light");
  };

  return (
    <header className="sticky top-0 z-50 border-b bg-background p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {/* Notification Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <Bell className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              <div className="px-3 py-2 text-sm text-muted-foreground">
                No requests yet
              </div>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Logo and Title */}
          <Link href={"/"} className="flex items-center space-x-2">
            <Image
              src="/images/LDIS.png"
              alt="LDIS"
              width={28}
              height={28}
              className="h-7 w-7"
            />
            <h1 className="text-xl font-extrabold">LDIS</h1>
          </Link>
        </div>

        {/* Burger Menu Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon">
              <Menu className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            {/* Theme Toggle */}
            <div className="px-3 py-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Sun className="h-4 w-4" />
                  <Label htmlFor="theme-toggle" className="text-sm">
                    Dark Mode
                  </Label>
                  <Moon className="h-4 w-4" />
                </div>
                <Switch
                  id="theme-toggle"
                  checked={isDarkMode}
                  onCheckedChange={handleThemeToggle}
                />
              </div>
            </div>

            <DropdownMenuSeparator />

            {/* Settings Button */}
            <DropdownMenuItem onClick={() => router.push("/settings")}>
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
}
