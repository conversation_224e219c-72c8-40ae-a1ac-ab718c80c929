"use client";

import { ThemeToggle } from "@/components/theme-toggle";
import Link from "next/link";
import Image from "next/image";
import { Settings } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";

export function Header() {
  const router = useRouter();

  return (
    <header className="sticky top-0 z-50 border-b bg-background p-4">
      <div className="flex items-center justify-between">
        <Link href={"/"} className="flex items-center space-x-2">
          <Image
            src="/images/LDIS.png"
            alt="LDIS"
            width={28}
            height={28}
            className="h-7 w-7"
          />
          <h1 className="text-xl font-extrabold">LDIS</h1>
        </Link>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="icon"
            onClick={() => router.push("/settings")}
          >
            <Settings className="h-4 w-4" />
          </Button>
          <ThemeToggle />
        </div>
      </div>
    </header>
  );
}
