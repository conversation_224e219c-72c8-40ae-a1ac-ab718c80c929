import CryptoJS from 'crypto-js';

// Secret key for encryption (in production, this should be from environment variables)
const SECRET_KEY = 'LDIS_AUTH_SECRET_2024';

export interface User {
  username: string;
  passwordHash: string;
  recoveryOptions: {
    privateKey?: string;
    securityQuestions?: {
      question: string;
      answerHash: string;
    }[];
  };
  createdAt: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface SignupData {
  username: string;
  password: string;
  recoveryMethod: 'privateKey' | 'securityQuestions';
  privateKey?: string;
  securityQuestions?: {
    question: string;
    answer: string;
  }[];
}

export interface RecoveryData {
  username: string;
  recoveryMethod: 'privateKey' | 'securityQuestions';
  privateKey?: string;
  securityAnswers?: string[];
}

// Security questions
export const SECURITY_QUESTIONS = [
  "What was the name of your childhood best friend?",
  "What is the name of the street you grew up on?",
  "What was the name of your first pet?",
  "What was your favorite subject in school?",
  "What is the middle name of your oldest sibling?"
];

// Encryption utilities
export const encrypt = (text: string): string => {
  return CryptoJS.AES.encrypt(text, SECRET_KEY).toString();
};

export const decrypt = (ciphertext: string): string => {
  const bytes = CryptoJS.AES.decrypt(ciphertext, SECRET_KEY);
  return bytes.toString(CryptoJS.enc.Utf8);
};

// Hash password
export const hashPassword = (password: string): string => {
  return CryptoJS.SHA256(password + SECRET_KEY).toString();
};

// Generate private key
export const generatePrivateKey = (): string => {
  return CryptoJS.lib.WordArray.random(32).toString();
};

// Storage utilities
const USERS_STORAGE_KEY = 'ldis_users';

export const getStoredUsers = (): User[] => {
  try {
    const encryptedData = localStorage.getItem(USERS_STORAGE_KEY);
    if (!encryptedData) return [];
    
    const decryptedData = decrypt(encryptedData);
    return JSON.parse(decryptedData);
  } catch (error) {
    console.error('Error loading users:', error);
    return [];
  }
};

export const saveUsers = (users: User[]): void => {
  try {
    const encryptedData = encrypt(JSON.stringify(users));
    localStorage.setItem(USERS_STORAGE_KEY, encryptedData);
  } catch (error) {
    console.error('Error saving users:', error);
  }
};

// Authentication functions
export const signup = (signupData: SignupData): { success: boolean; message: string; privateKey?: string } => {
  try {
    const users = getStoredUsers();
    
    // Check if username already exists
    if (users.find(user => user.username === signupData.username)) {
      return { success: false, message: 'Username already exists' };
    }

    // Create new user
    const newUser: User = {
      username: signupData.username,
      passwordHash: hashPassword(signupData.password),
      recoveryOptions: {},
      createdAt: new Date().toISOString()
    };

    // Set recovery options
    if (signupData.recoveryMethod === 'privateKey') {
      const privateKey = signupData.privateKey || generatePrivateKey();
      newUser.recoveryOptions.privateKey = encrypt(privateKey);
      
      users.push(newUser);
      saveUsers(users);
      
      return { 
        success: true, 
        message: 'Account created successfully', 
        privateKey: privateKey 
      };
    } else if (signupData.recoveryMethod === 'securityQuestions' && signupData.securityQuestions) {
      newUser.recoveryOptions.securityQuestions = signupData.securityQuestions.map(sq => ({
        question: sq.question,
        answerHash: hashPassword(sq.answer.toLowerCase().trim())
      }));
      
      users.push(newUser);
      saveUsers(users);
      
      return { success: true, message: 'Account created successfully' };
    }

    return { success: false, message: 'Invalid recovery method' };
  } catch (error) {
    console.error('Signup error:', error);
    return { success: false, message: 'An error occurred during signup' };
  }
};

export const login = (credentials: LoginCredentials): { success: boolean; message: string } => {
  try {
    const users = getStoredUsers();
    const user = users.find(u => u.username === credentials.username);
    
    if (!user) {
      return { success: false, message: 'Invalid username or password' };
    }

    const passwordHash = hashPassword(credentials.password);
    if (user.passwordHash !== passwordHash) {
      return { success: false, message: 'Invalid username or password' };
    }

    // Set admin mode
    localStorage.setItem('adminMode', 'true');
    localStorage.setItem('currentUser', credentials.username);
    window.dispatchEvent(new Event('adminModeChanged'));

    return { success: true, message: 'Login successful' };
  } catch (error) {
    console.error('Login error:', error);
    return { success: false, message: 'An error occurred during login' };
  }
};

export const recoverPassword = (recoveryData: RecoveryData, newPassword: string): { success: boolean; message: string } => {
  try {
    const users = getStoredUsers();
    const userIndex = users.findIndex(u => u.username === recoveryData.username);
    
    if (userIndex === -1) {
      return { success: false, message: 'User not found' };
    }

    const user = users[userIndex];

    // Verify recovery method
    if (recoveryData.recoveryMethod === 'privateKey') {
      if (!user.recoveryOptions.privateKey || !recoveryData.privateKey) {
        return { success: false, message: 'Invalid recovery method' };
      }

      const storedPrivateKey = decrypt(user.recoveryOptions.privateKey);
      if (storedPrivateKey !== recoveryData.privateKey) {
        return { success: false, message: 'Invalid private key' };
      }
    } else if (recoveryData.recoveryMethod === 'securityQuestions') {
      if (!user.recoveryOptions.securityQuestions || !recoveryData.securityAnswers) {
        return { success: false, message: 'Invalid recovery method' };
      }

      // Verify all security answers
      for (let i = 0; i < user.recoveryOptions.securityQuestions.length; i++) {
        const expectedHash = user.recoveryOptions.securityQuestions[i].answerHash;
        const providedHash = hashPassword(recoveryData.securityAnswers[i].toLowerCase().trim());
        
        if (expectedHash !== providedHash) {
          return { success: false, message: 'Incorrect security answers' };
        }
      }
    }

    // Update password
    users[userIndex].passwordHash = hashPassword(newPassword);
    saveUsers(users);

    return { success: true, message: 'Password reset successfully' };
  } catch (error) {
    console.error('Password recovery error:', error);
    return { success: false, message: 'An error occurred during password recovery' };
  }
};

export const logout = (): void => {
  localStorage.setItem('adminMode', 'false');
  localStorage.removeItem('currentUser');
  window.dispatchEvent(new Event('adminModeChanged'));
};

export const getCurrentUser = (): string | null => {
  return localStorage.getItem('currentUser');
};

export const getUserRecoveryOptions = (username: string): { hasPrivateKey: boolean; securityQuestions: string[] } => {
  const users = getStoredUsers();
  const user = users.find(u => u.username === username);
  
  if (!user) {
    return { hasPrivateKey: false, securityQuestions: [] };
  }

  return {
    hasPrivateKey: !!user.recoveryOptions.privateKey,
    securityQuestions: user.recoveryOptions.securityQuestions?.map(sq => sq.question) || []
  };
};
